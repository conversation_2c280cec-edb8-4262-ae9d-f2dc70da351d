"""
Qdrant client for vector database operations.
Handles document embeddings and semantic search.
"""
import logging
from typing import List, Dict, Any, Optional
import asyncio
from qdrant_client import QdrantClient as QdrantSyncClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct

from config.settings import QdrantSettings


logger = logging.getLogger(__name__)


class QdrantClient:
    """Async wrapper for Qdrant operations."""
    
    def __init__(self, settings: QdrantSettings):
        self.settings = settings
        self.client: Optional[QdrantSyncClient] = None
        self.collection_name = settings.collection_name

    async def initialize(self):
        """Initialize the Qdrant client."""
        try:
            self.client = QdrantSyncClient(
                host=self.settings.host,
                port=self.settings.port
            )
            
            # Ensure collection exists
            await self._ensure_collection_exists()
            
            logger.info("Qdrant client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant client: {e}")
            raise

    async def close(self):
        """Close the Qdrant client."""
        if self.client:
            self.client.close()
        logger.info("Qdrant client closed")

    async def _ensure_collection_exists(self):
        """Ensure the collection exists, create if it doesn't."""
        try:
            loop = asyncio.get_event_loop()
            
            # Check if collection exists
            collections = await loop.run_in_executor(
                None, self.client.get_collections
            )
            
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                # Create collection with default vector configuration
                await loop.run_in_executor(
                    None,
                    self.client.create_collection,
                    self.collection_name,
                    VectorParams(size=384, distance=Distance.COSINE)  # Default for sentence transformers
                )
                logger.info(f"Created collection: {self.collection_name}")
            else:
                logger.info(f"Collection exists: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Error ensuring collection exists: {e}")
            raise

    async def upsert_points(self, points: List[PointStruct]) -> bool:
        """Upsert points into the collection."""
        try:
            loop = asyncio.get_event_loop()
            
            await loop.run_in_executor(
                None,
                self.client.upsert,
                self.collection_name,
                points
            )
            
            logger.info(f"Upserted {len(points)} points to collection {self.collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error upserting points: {e}")
            return False

    async def search(self, query_vector: List[float], limit: int = 10, 
                    score_threshold: Optional[float] = None,
                    filter_conditions: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search for similar vectors."""
        try:
            loop = asyncio.get_event_loop()
            
            search_params = {
                "collection_name": self.collection_name,
                "query_vector": query_vector,
                "limit": limit
            }
            
            if score_threshold is not None:
                search_params["score_threshold"] = score_threshold
                
            if filter_conditions:
                search_params["query_filter"] = models.Filter(**filter_conditions)
            
            results = await loop.run_in_executor(
                None,
                lambda: self.client.search(**search_params)
            )
            
            # Convert results to dict format
            search_results = []
            for result in results:
                search_results.append({
                    "id": result.id,
                    "score": result.score,
                    "payload": result.payload,
                    "vector": result.vector
                })
            
            logger.info(f"Found {len(search_results)} similar vectors")
            return search_results
            
        except Exception as e:
            logger.error(f"Error searching vectors: {e}")
            return []

    async def get_point(self, point_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific point by ID."""
        try:
            loop = asyncio.get_event_loop()
            
            result = await loop.run_in_executor(
                None,
                self.client.retrieve,
                self.collection_name,
                [point_id]
            )
            
            if result:
                point = result[0]
                return {
                    "id": point.id,
                    "payload": point.payload,
                    "vector": point.vector
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting point {point_id}: {e}")
            return None

    async def delete_points(self, point_ids: List[str]) -> bool:
        """Delete points by IDs."""
        try:
            loop = asyncio.get_event_loop()
            
            await loop.run_in_executor(
                None,
                self.client.delete,
                self.collection_name,
                point_ids
            )
            
            logger.info(f"Deleted {len(point_ids)} points from collection {self.collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting points: {e}")
            return False

    async def count_points(self) -> int:
        """Count total points in the collection."""
        try:
            loop = asyncio.get_event_loop()
            
            info = await loop.run_in_executor(
                None,
                self.client.get_collection,
                self.collection_name
            )
            
            return info.points_count
            
        except Exception as e:
            logger.error(f"Error counting points: {e}")
            return 0

    async def create_index(self, field_name: str, field_type: str = "keyword") -> bool:
        """Create an index on a payload field."""
        try:
            loop = asyncio.get_event_loop()
            
            await loop.run_in_executor(
                None,
                self.client.create_payload_index,
                self.collection_name,
                field_name,
                field_type
            )
            
            logger.info(f"Created index on field {field_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating index on field {field_name}: {e}")
            return False

    async def scroll_points(self, limit: int = 100, offset: Optional[str] = None) -> Dict[str, Any]:
        """Scroll through points in the collection."""
        try:
            loop = asyncio.get_event_loop()
            
            result = await loop.run_in_executor(
                None,
                self.client.scroll,
                self.collection_name,
                limit,
                offset
            )
            
            points = []
            for point in result[0]:
                points.append({
                    "id": point.id,
                    "payload": point.payload,
                    "vector": point.vector
                })
            
            return {
                "points": points,
                "next_page_offset": result[1]
            }
            
        except Exception as e:
            logger.error(f"Error scrolling points: {e}")
            return {"points": [], "next_page_offset": None}
