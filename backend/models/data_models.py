"""
Data models for the RDF Agent system.
Defines Pydantic models for data validation and serialization.
"""
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum


class MessageType(str, Enum):
    """Types of messages in the system."""
    BUILDING_DATA = "building_data"
    ADDRESS_DATA = "address_data"
    DOCUMENT_PROCESSING = "document_processing"
    QUERY_REQUEST = "query_request"
    QUERY_RESPONSE = "query_response"


class ProcessingStatus(str, Enum):
    """Status of processing operations."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class KafkaMessage(BaseModel):
    """Base model for Kafka messages."""
    message_type: MessageType
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = None
    payload: Dict[str, Any]


class BuildingData(BaseModel):
    """Model for building data from Kafka."""
    building_id: str
    name: Optional[str] = None
    address: Optional[str] = None
    coordinates: Optional[Dict[str, float]] = None
    properties: Dict[str, Any] = Field(default_factory=dict)
    rdf_graph: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AddressData(BaseModel):
    """Model for address data from Kafka."""
    address_id: str
    street: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    coordinates: Optional[Dict[str, float]] = None
    properties: Dict[str, Any] = Field(default_factory=dict)
    rdf_graph: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class DocumentInfo(BaseModel):
    """Model for document information."""
    document_id: str
    filename: str
    file_path: str
    mime_type: str
    size: int
    checksum: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ProcessingTask(BaseModel):
    """Model for document processing tasks."""
    task_id: str
    document_info: DocumentInfo
    status: ProcessingStatus = ProcessingStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


class QueryRequest(BaseModel):
    """Model for query requests."""
    query_id: str
    session_id: Optional[str] = None
    query_text: str
    query_type: str = "natural_language"  # or "sparql"
    context: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class QueryResponse(BaseModel):
    """Model for query responses."""
    query_id: str
    session_id: Optional[str] = None
    response_text: str
    sparql_query: Optional[str] = None
    results: Optional[List[Dict[str, Any]]] = None
    confidence: Optional[float] = None
    sources: List[str] = Field(default_factory=list)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    processing_time: Optional[float] = None


class ConversationTurn(BaseModel):
    """Model for conversation turns."""
    turn_id: str
    session_id: str
    user_message: str
    assistant_response: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Session(BaseModel):
    """Model for user sessions."""
    session_id: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    turns: List[ConversationTurn] = Field(default_factory=list)


class TTLConversionResult(BaseModel):
    """Model for TTL conversion results."""
    success: bool
    ttl_content: Optional[str] = None
    error_message: Optional[str] = None
    triples_count: Optional[int] = None
    namespaces: Dict[str, str] = Field(default_factory=dict)


class DoclingResult(BaseModel):
    """Model for Docling processing results."""
    success: bool
    markdown_content: Optional[str] = None
    json_content: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
