"""
Central configuration management using pydantic-settings.
Loads configuration from config.yaml and .env files.
Provides type-safe access to all application settings.
"""
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field
from typing import Dict, Any


class KafkaSettings(BaseSettings):
    bootstrap_servers: str = Field(default="localhost:9092")
    group_id: str = Field(default="rdf-agent-consumer")
    usecase_topic_pattern: str = Field(default="data.usecases.*")


class GraphDBSettings(BaseSettings):
    url: str = Field(default="http://localhost:7200")
    default_repository: str = Field(default="default")


class MinIOSettings(BaseSettings):
    endpoint: str = Field(default="localhost:9000")
    access_key: str = Field(default="minioadmin")
    secret_key: str = Field(default="minioadmin")
    bucket_name: str = Field(default="data")


class QdrantSettings(BaseSettings):
    host: str = Field(default="localhost")
    port: int = Field(default=6333)
    collection_name: str = Field(default="documents")


class DoclingSettings(BaseSettings):
    """Configuration for Docling document extraction."""
    enabled: bool = Field(default=True)
    output_format: str = Field(default="markdown")  # Can be "markdown", "json", "text"
    timeout: int = Field(default=300)  # Timeout in seconds for document processing


class DatabaseSettings(BaseSettings):
    url: str = Field(default="sqlite:///./app.db")


class AISettings(BaseSettings):
    model: str = Field(default="openai:gpt-4o")
    api_key: str = Field(default="")
    or_api_key: str = Field(default="")  # OpenRouter API key


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_nested_delimiter="__",
        extra="ignore"  # Ignore extra fields from .env
    )
    
    kafka: KafkaSettings = KafkaSettings()
    graphdb: GraphDBSettings = GraphDBSettings()
    minio: MinIOSettings = MinIOSettings()
    qdrant: QdrantSettings = QdrantSettings()
    docling: DoclingSettings = DoclingSettings()
    database: DatabaseSettings = DatabaseSettings()
    ai: AISettings = AISettings()
