#!/usr/bin/env python3
"""
Test document processing pipeline.
"""
import asyncio
import json
import logging
from pathlib import Path
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_minio_document_storage():
    """Test MinIO document storage functionality."""
    try:
        from core.dependencies import get_dependencies, cleanup_dependencies
        
        logger.info("Testing MinIO document storage...")
        
        # Initialize dependencies
        deps = await get_dependencies()
        
        # Test document upload
        test_content = b"This is a test document content for MinIO storage testing."
        test_path = "test/documents/test_document.txt"
        
        # Upload test document
        success = await deps.minio_client.upload_data(test_content, test_path)
        if success:
            logger.info(f"✓ Successfully uploaded test document to MinIO")
        else:
            logger.error(f"✗ Failed to upload test document to MinIO")
            return False

        # Retrieve test document
        retrieved_content = await deps.minio_client.download_data(test_path)
        if retrieved_content == test_content:
            logger.info(f"✓ Successfully retrieved test document from MinIO")
        else:
            logger.error(f"✗ Retrieved content doesn't match uploaded content")
            return False
        
        # List objects
        objects = await deps.minio_client.list_objects("test/")
        logger.info(f"✓ Found {len(objects)} objects in test/ prefix")
        
        # Clean up
        await deps.minio_client.delete_object(test_path)
        logger.info(f"✓ Cleaned up test document")
        
        await cleanup_dependencies()
        
        logger.info("✓ MinIO document storage test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"MinIO document storage test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_qdrant_vector_storage():
    """Test Qdrant vector storage functionality."""
    try:
        from core.dependencies import get_dependencies, cleanup_dependencies
        from agents.rag_agent import RAGAgent
        
        logger.info("Testing Qdrant vector storage...")
        
        # Initialize dependencies
        deps = await get_dependencies()
        rag_agent = RAGAgent(deps)
        
        # Test document addition (use UUID format for Qdrant)
        import uuid
        test_doc_id = str(uuid.uuid4())
        test_content = "This is a test document for vector storage. It contains information about building certificates and energy efficiency ratings."
        test_metadata = {
            "document_type": "certificate",
            "building_id": "test_building_001",
            "usecase": "test_case"
        }
        
        success = await rag_agent.add_document(
            document_id=test_doc_id,
            content=test_content,
            metadata=test_metadata
        )
        
        if success:
            logger.info(f"✓ Successfully added document to vector database")
        else:
            logger.error(f"✗ Failed to add document to vector database")
            return False
        
        # Test document search
        search_results = await rag_agent.search_documents(
            query="building certificates energy efficiency",
            limit=5
        )
        
        if search_results and search_results.get('results'):
            results = search_results['results']
            logger.info(f"✓ Successfully searched documents: found {len(results)} results")
            for i, result in enumerate(results[:2]):
                score = result.get('score', 0)
                logger.info(f"  Result {i+1}: score={score:.3f}")
        else:
            logger.error(f"✗ No search results found")
            return False
        
        # Clean up - delete test document
        try:
            await rag_agent.delete_document(test_doc_id)
            logger.info(f"✓ Cleaned up test document from vector database")
        except Exception as e:
            logger.warning(f"Could not clean up test document: {e}")
        
        await cleanup_dependencies()
        
        logger.info("✓ Qdrant vector storage test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Qdrant vector storage test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_graphdb_rdf_storage():
    """Test GraphDB RDF storage functionality."""
    try:
        from core.dependencies import get_dependencies, cleanup_dependencies
        
        logger.info("Testing GraphDB RDF storage...")
        
        # Initialize dependencies
        deps = await get_dependencies()
        
        # Test TTL data upload
        test_ttl = """
        @prefix ex: <http://example.org/> .
        @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
        @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
        
        ex:TestBuilding rdf:type ex:Building ;
                       rdfs:label "Test Building" ;
                       ex:hasAddress "123 Test Street" ;
                       ex:hasEnergyRating "A+" .
        """
        
        # Upload TTL data to default repository
        success = await deps.graphdb_client.upload_ttl_data(test_ttl)
        if success:
            logger.info(f"✓ Successfully uploaded TTL data to GraphDB")
        else:
            logger.error(f"✗ Failed to upload TTL data to GraphDB")
            return False
        
        # Test SPARQL query
        sparql_query = """
        PREFIX ex: <http://example.org/>
        PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
        
        SELECT ?building ?label ?address ?rating WHERE {
            ?building a ex:Building ;
                     rdfs:label ?label ;
                     ex:hasAddress ?address ;
                     ex:hasEnergyRating ?rating .
        }
        """
        
        results = await deps.graphdb_client.execute_sparql_query(sparql_query)
        if results and len(results) > 0:
            logger.info(f"✓ Successfully executed SPARQL query: found {len(results)} results")
            for result in results:
                if isinstance(result, dict):
                    label = result.get('label', {})
                    if isinstance(label, dict):
                        building_name = label.get('value', 'N/A')
                    else:
                        building_name = str(label)
                else:
                    building_name = str(result)
                logger.info(f"  Building: {building_name}")
        else:
            logger.error(f"✗ No SPARQL query results found")
            return False
        
        await cleanup_dependencies()
        
        logger.info("✓ GraphDB RDF storage test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"GraphDB RDF storage test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_ai_agents():
    """Test AI agents functionality."""
    try:
        from core.dependencies import get_dependencies, cleanup_dependencies
        from agents.rdf_query_agent import RDFQueryAgent
        from agents.rag_agent import RAGAgent
        from agents.orchestrator_agent import OrchestratorAgent
        
        logger.info("Testing AI agents...")
        
        # Initialize dependencies
        deps = await get_dependencies()
        
        # Test RDF Query Agent
        rdf_agent = RDFQueryAgent(deps)
        rdf_query = "Find all buildings with energy rating A+"
        rdf_result = await rdf_agent.process_query(rdf_query)
        
        if rdf_result:
            logger.info(f"✓ RDF Query Agent responded successfully")
            logger.info(f"  Response length: {len(rdf_result)} characters")
        else:
            logger.error(f"✗ RDF Query Agent failed to respond")
            return False
        
        # Test RAG Agent
        rag_agent = RAGAgent(deps)
        rag_query = "What information do you have about building certificates?"
        rag_result = await rag_agent.search_documents(rag_query)

        if rag_result and not rag_result.get('error'):
            logger.info(f"✓ RAG Agent responded successfully")
            logger.info(f"  Response: {rag_result}")
        else:
            logger.error(f"✗ RAG Agent failed to respond: {rag_result.get('error', 'Unknown error')}")
            return False
        
        # Test Orchestrator Agent
        orchestrator = OrchestratorAgent(deps)
        orchestrator_query = "Tell me about energy efficient buildings and their certificates"
        orchestrator_result = await orchestrator.process_query(orchestrator_query)
        
        if orchestrator_result:
            logger.info(f"✓ Orchestrator Agent responded successfully")
            logger.info(f"  Response length: {len(orchestrator_result)} characters")
        else:
            logger.error(f"✗ Orchestrator Agent failed to respond")
            return False
        
        await cleanup_dependencies()
        
        logger.info("✓ AI agents test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"AI agents test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all document processing tests."""
    logger.info("Starting document processing tests...")
    
    results = []
    
    # Test MinIO document storage
    results.append(await test_minio_document_storage())
    
    # Test Qdrant vector storage
    results.append(await test_qdrant_vector_storage())
    
    # Test GraphDB RDF storage
    results.append(await test_graphdb_rdf_storage())
    
    # Test AI agents
    results.append(await test_ai_agents())
    
    if all(results):
        logger.info("✓ All document processing tests passed!")
        return True
    else:
        logger.error("✗ Some document processing tests failed!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
