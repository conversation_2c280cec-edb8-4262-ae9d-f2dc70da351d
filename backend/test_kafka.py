#!/usr/bin/env python3
"""
Test Kafka message processing with example data.
"""
import asyncio
import json
import logging
from pathlib import Path
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_kafka_producer():
    """Test Kafka producer with example data."""
    try:
        from infrastructure.kafka_client import KafkaClient
        from config.settings import Settings
        
        logger.info("Testing Kafka producer...")
        
        # Initialize Kafka client
        settings = Settings()
        kafka_client = KafkaClient(settings.kafka)
        await kafka_client.initialize()
        
        # Load example data
        example_file = Path("../.context/example_files/example_input.json")
        if not example_file.exists():
            logger.error("Example data file not found")
            return False
        
        with open(example_file, 'r') as f:
            example_data = json.load(f)
        
        logger.info(f"Loaded {len(example_data)} example items")
        
        # Test with first 3 items
        topic = "usecases.sample_input"
        success_count = 0
        
        for i, item in enumerate(example_data[:3]):
            try:
                logger.info(f"Sending item {i+1}...")
                await kafka_client.produce_message(topic, item)
                success_count += 1
                logger.info(f"✓ Item {i+1} sent successfully")
            except Exception as e:
                logger.error(f"✗ Failed to send item {i+1}: {e}")
        
        await kafka_client.close()
        
        logger.info(f"✓ Kafka producer test completed: {success_count}/3 messages sent")
        return success_count == 3
        
    except Exception as e:
        logger.error(f"Kafka producer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_kafka_consumer():
    """Test Kafka consumer functionality."""
    try:
        from infrastructure.kafka_client import KafkaClient
        from config.settings import Settings
        
        logger.info("Testing Kafka consumer...")
        
        # Initialize Kafka client
        settings = Settings()
        kafka_client = KafkaClient(settings.kafka)
        await kafka_client.initialize()
        
        # Test consumer setup
        topic = "usecases.sample_input"
        messages_received = []
        
        async def message_handler(message: Dict[str, Any], topic: str):
            """Handle received messages."""
            logger.info(f"Received message from topic {topic}")
            messages_received.append(message)
        
        # Subscribe to topic
        await kafka_client.subscribe_to_topics([topic])
        
        # Consume messages for a short time
        logger.info("Consuming messages for 5 seconds...")
        try:
            await asyncio.wait_for(
                kafka_client.start_consuming(),
                timeout=5.0
            )
        except asyncio.TimeoutError:
            logger.info("Consumer timeout reached")
        
        await kafka_client.close()
        
        logger.info(f"✓ Kafka consumer test completed: {len(messages_received)} messages received")
        return True
        
    except Exception as e:
        logger.error(f"Kafka consumer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_kafka_message_processing():
    """Test the full Kafka message processing pipeline."""
    try:
        from services.kafka_processor import KafkaProcessor
        from core.dependencies import get_dependencies, cleanup_dependencies
        
        logger.info("Testing Kafka message processing pipeline...")
        
        # Initialize dependencies
        deps = await get_dependencies()
        
        # Create Kafka processor
        processor = KafkaProcessor(deps)
        
        # Load example data
        example_file = Path("../.context/example_files/example_input.json")
        if not example_file.exists():
            logger.error("Example data file not found")
            return False
        
        with open(example_file, 'r') as f:
            example_data = json.load(f)
        
        # Process first item
        test_item = example_data[0]
        topic = "usecases.sample_input"
        
        logger.info("Processing test message...")
        # Create a simple handler to process the message
        await processor._handle_usecase_message(topic, test_item)
        
        await cleanup_dependencies()
        
        logger.info("✓ Kafka message processing test completed")
        return True
        
    except Exception as e:
        logger.error(f"Kafka message processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_ttl_conversion_with_example_data():
    """Test TTL conversion with actual example data."""
    try:
        from services.ttl_converter import TTLConverter
        
        logger.info("Testing TTL conversion with example data...")
        
        # Load example data
        example_file = Path("../.context/example_files/example_input.json")
        if not example_file.exists():
            logger.error("Example data file not found")
            return False
        
        with open(example_file, 'r') as f:
            example_data = json.load(f)
        
        ttl_converter = TTLConverter()
        success_count = 0
        
        # Test first 3 items
        for i, item in enumerate(example_data[:3]):
            try:
                logger.info(f"Converting item {i+1} to TTL...")
                
                # Convert the raw JSON to TTL
                result = await ttl_converter.convert_json_to_ttl(item)
                
                if result.success:
                    logger.info(f"✓ Item {i+1}: {result.triples_count} triples generated")
                    success_count += 1
                else:
                    logger.error(f"✗ Item {i+1} conversion failed: {result.error_message}")
                    
            except Exception as e:
                logger.error(f"✗ Error converting item {i+1}: {e}")
        
        logger.info(f"✓ TTL conversion test completed: {success_count}/3 items converted")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"TTL conversion test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all Kafka tests."""
    logger.info("Starting Kafka tests...")
    
    results = []
    
    # Test TTL conversion first
    results.append(await test_ttl_conversion_with_example_data())
    
    # Test Kafka producer
    results.append(await test_kafka_producer())
    
    # Test Kafka consumer
    results.append(await test_kafka_consumer())
    
    # Test message processing pipeline
    results.append(await test_kafka_message_processing())
    
    if all(results):
        logger.info("✓ All Kafka tests passed!")
        return True
    else:
        logger.error("✗ Some Kafka tests failed!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
