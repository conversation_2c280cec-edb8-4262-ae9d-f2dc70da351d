"""
Session manager for handling user sessions and conversation history.
Manages SQLite-based session storage and retrieval.
"""
import logging
import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime

from infrastructure.database import DatabaseManager
from models.data_models import Session, ConversationTurn


logger = logging.getLogger(__name__)


class SessionManager:
    """Manager for user sessions and conversation history."""
    
    def __init__(self, database_manager: DatabaseManager):
        self.db = database_manager

    async def create_session(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Create a new session and return the session ID."""
        session_id = str(uuid.uuid4())
        
        success = await self.db.create_session(session_id, metadata)
        if success:
            logger.info(f"Created new session: {session_id}")
            return session_id
        else:
            raise Exception(f"Failed to create session: {session_id}")

    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID."""
        session = await self.db.get_session(session_id)
        if session:
            logger.debug(f"Retrieved session: {session_id}")
        else:
            logger.warning(f"Session not found: {session_id}")
        return session

    async def add_conversation_turn(self, session_id: str, user_message: str, 
                                  assistant_response: str,
                                  metadata: Optional[Dict[str, Any]] = None) -> str:
        """Add a conversation turn to a session."""
        turn_id = str(uuid.uuid4())
        
        turn = ConversationTurn(
            turn_id=turn_id,
            session_id=session_id,
            user_message=user_message,
            assistant_response=assistant_response,
            timestamp=datetime.utcnow(),
            metadata=metadata or {}
        )
        
        success = await self.db.add_conversation_turn(turn)
        if success:
            logger.info(f"Added conversation turn: {turn_id} to session: {session_id}")
            return turn_id
        else:
            raise Exception(f"Failed to add conversation turn to session: {session_id}")

    async def get_conversation_history(self, session_id: str, 
                                     limit: Optional[int] = None) -> List[ConversationTurn]:
        """Get conversation history for a session."""
        session = await self.get_session(session_id)
        if not session:
            return []
        
        turns = session.turns
        if limit:
            turns = turns[-limit:]  # Get last N turns
        
        logger.debug(f"Retrieved {len(turns)} conversation turns for session: {session_id}")
        return turns

    async def get_recent_context(self, session_id: str, 
                               max_turns: int = 5) -> str:
        """Get recent conversation context as formatted string."""
        turns = await self.get_conversation_history(session_id, limit=max_turns)
        
        if not turns:
            return ""
        
        context_parts = []
        for turn in turns:
            context_parts.append(f"User: {turn.user_message}")
            context_parts.append(f"Assistant: {turn.assistant_response}")
        
        context = "\n\n".join(context_parts)
        logger.debug(f"Generated context for session {session_id}: {len(context)} characters")
        return context

    async def session_exists(self, session_id: str) -> bool:
        """Check if a session exists."""
        session = await self.get_session(session_id)
        return session is not None

    async def update_session_metadata(self, session_id: str, 
                                    metadata: Dict[str, Any]) -> bool:
        """Update session metadata."""
        try:
            # Get current session
            session = await self.get_session(session_id)
            if not session:
                logger.warning(f"Cannot update metadata for non-existent session: {session_id}")
                return False
            
            # Update metadata (merge with existing)
            updated_metadata = {**session.metadata, **metadata}
            
            # Update in database
            import json
            await self.db.connection.execute("""
                UPDATE sessions SET metadata = ?, updated_at = CURRENT_TIMESTAMP
                WHERE session_id = ?
            """, (json.dumps(updated_metadata), session_id))
            
            await self.db.connection.commit()
            
            logger.info(f"Updated metadata for session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating session metadata: {e}")
            return False

    async def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of session statistics."""
        session = await self.get_session(session_id)
        if not session:
            return None
        
        summary = {
            "session_id": session_id,
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "total_turns": len(session.turns),
            "metadata": session.metadata
        }
        
        if session.turns:
            summary["first_message_at"] = session.turns[0].timestamp.isoformat()
            summary["last_message_at"] = session.turns[-1].timestamp.isoformat()
            
            # Calculate total message lengths
            total_user_chars = sum(len(turn.user_message) for turn in session.turns)
            total_assistant_chars = sum(len(turn.assistant_response) for turn in session.turns)
            
            summary["total_user_characters"] = total_user_chars
            summary["total_assistant_characters"] = total_assistant_chars
        
        return summary

    async def cleanup_old_sessions(self, days_old: int = 30) -> int:
        """Clean up sessions older than specified days."""
        try:
            from datetime import timedelta
            
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            # Delete old conversation turns first (foreign key constraint)
            await self.db.connection.execute("""
                DELETE FROM conversation_turns 
                WHERE session_id IN (
                    SELECT session_id FROM sessions 
                    WHERE updated_at < ?
                )
            """, (cutoff_date.isoformat(),))
            
            # Delete old sessions
            cursor = await self.db.connection.execute("""
                DELETE FROM sessions WHERE updated_at < ?
            """, (cutoff_date.isoformat(),))
            
            deleted_count = cursor.rowcount
            await self.db.connection.commit()
            
            logger.info(f"Cleaned up {deleted_count} old sessions")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up old sessions: {e}")
            return 0

    async def get_all_sessions(self, limit: Optional[int] = None, 
                             offset: int = 0) -> List[Dict[str, Any]]:
        """Get all sessions with basic information."""
        try:
            import json
            
            query = """
                SELECT session_id, created_at, updated_at, metadata,
                       (SELECT COUNT(*) FROM conversation_turns ct 
                        WHERE ct.session_id = s.session_id) as turn_count
                FROM sessions s
                ORDER BY updated_at DESC
            """
            
            if limit:
                query += f" LIMIT {limit} OFFSET {offset}"
            
            cursor = await self.db.connection.execute(query)
            rows = await cursor.fetchall()
            
            sessions = []
            for row in rows:
                sessions.append({
                    "session_id": row[0],
                    "created_at": row[1],
                    "updated_at": row[2],
                    "metadata": json.loads(row[3]),
                    "turn_count": row[4]
                })
            
            logger.debug(f"Retrieved {len(sessions)} sessions")
            return sessions
            
        except Exception as e:
            logger.error(f"Error getting all sessions: {e}")
            return []
