"""
TTL converter service for converting JSON data to RDF/TTL format.
Handles building and address data conversion to RDF triples.
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

from rdflib import Graph, Namespace, URIRef, Literal, BNode
from rdflib.namespace import RDF, RDFS, XSD

from models.data_models import TTLConversionResult, BuildingData, AddressData


logger = logging.getLogger(__name__)


class TTLConverter:
    """Service for converting JSON data to TTL format."""
    
    def __init__(self):
        # Define namespaces
        self.BUILDING = Namespace("http://example.org/building/")
        self.ADDRESS = Namespace("http://example.org/address/")
        self.SCHEMA = Namespace("http://schema.org/")
        self.GEO = Namespace("http://www.w3.org/2003/01/geo/wgs84_pos#")
        
        # Common namespaces
        self.namespaces = {
            "building": self.BUILDING,
            "address": self.ADDRESS,
            "schema": self.SCHEMA,
            "geo": self.GEO,
            "rdf": RDF,
            "rdfs": RDFS,
            "xsd": XSD
        }

    async def convert_building_data(self, building_data: BuildingData) -> TTLConversionResult:
        """Convert building data to TTL format."""
        try:
            graph = Graph()
            
            # Bind namespaces
            for prefix, namespace in self.namespaces.items():
                graph.bind(prefix, namespace)
            
            # Create building URI
            building_uri = self.BUILDING[building_data.building_id]
            
            # Add basic building information
            graph.add((building_uri, RDF.type, self.SCHEMA.Building))
            
            if building_data.name:
                graph.add((building_uri, self.SCHEMA.name, Literal(building_data.name)))
            
            if building_data.address:
                graph.add((building_uri, self.SCHEMA.address, Literal(building_data.address)))
            
            # Add coordinates if available
            if building_data.coordinates:
                self._add_coordinates(graph, building_uri, building_data.coordinates)
            
            # Add properties
            for key, value in building_data.properties.items():
                property_uri = self.BUILDING[f"property_{key}"]
                graph.add((building_uri, property_uri, self._convert_value(value)))
            
            # Add metadata
            if building_data.metadata:
                self._add_metadata(graph, building_uri, building_data.metadata)
            
            # If RDF graph is provided, merge it
            if building_data.rdf_graph:
                try:
                    temp_graph = Graph()
                    temp_graph.parse(data=building_data.rdf_graph, format="turtle")
                    graph += temp_graph
                except Exception as e:
                    logger.warning(f"Failed to parse provided RDF graph: {e}")
            
            # Serialize to TTL
            ttl_content = graph.serialize(format="turtle")
            triples_count = len(graph)
            
            logger.info(f"Converted building data to TTL: {triples_count} triples")
            
            return TTLConversionResult(
                success=True,
                ttl_content=ttl_content,
                triples_count=triples_count,
                namespaces={prefix: str(ns) for prefix, ns in self.namespaces.items()}
            )
            
        except Exception as e:
            error_msg = f"Error converting building data to TTL: {str(e)}"
            logger.error(error_msg)
            return TTLConversionResult(
                success=False,
                error_message=error_msg
            )

    async def convert_address_data(self, address_data: AddressData) -> TTLConversionResult:
        """Convert address data to TTL format."""
        try:
            graph = Graph()
            
            # Bind namespaces
            for prefix, namespace in self.namespaces.items():
                graph.bind(prefix, namespace)
            
            # Create address URI
            address_uri = self.ADDRESS[address_data.address_id]
            
            # Add basic address information
            graph.add((address_uri, RDF.type, self.SCHEMA.PostalAddress))
            
            if address_data.street:
                graph.add((address_uri, self.SCHEMA.streetAddress, Literal(address_data.street)))
            
            if address_data.city:
                graph.add((address_uri, self.SCHEMA.addressLocality, Literal(address_data.city)))
            
            if address_data.postal_code:
                graph.add((address_uri, self.SCHEMA.postalCode, Literal(address_data.postal_code)))
            
            if address_data.country:
                graph.add((address_uri, self.SCHEMA.addressCountry, Literal(address_data.country)))
            
            # Add coordinates if available
            if address_data.coordinates:
                self._add_coordinates(graph, address_uri, address_data.coordinates)
            
            # Add properties
            for key, value in address_data.properties.items():
                property_uri = self.ADDRESS[f"property_{key}"]
                graph.add((address_uri, property_uri, self._convert_value(value)))
            
            # Add metadata
            if address_data.metadata:
                self._add_metadata(graph, address_uri, address_data.metadata)
            
            # If RDF graph is provided, merge it
            if address_data.rdf_graph:
                try:
                    temp_graph = Graph()
                    temp_graph.parse(data=address_data.rdf_graph, format="turtle")
                    graph += temp_graph
                except Exception as e:
                    logger.warning(f"Failed to parse provided RDF graph: {e}")
            
            # Serialize to TTL
            ttl_content = graph.serialize(format="turtle")
            triples_count = len(graph)
            
            logger.info(f"Converted address data to TTL: {triples_count} triples")
            
            return TTLConversionResult(
                success=True,
                ttl_content=ttl_content,
                triples_count=triples_count,
                namespaces={prefix: str(ns) for prefix, ns in self.namespaces.items()}
            )
            
        except Exception as e:
            error_msg = f"Error converting address data to TTL: {str(e)}"
            logger.error(error_msg)
            return TTLConversionResult(
                success=False,
                error_message=error_msg
            )

    async def convert_json_to_ttl(self, json_data: Dict[str, Any], 
                                 base_uri: Optional[str] = None) -> TTLConversionResult:
        """Convert generic JSON data to TTL format."""
        try:
            graph = Graph()
            
            # Bind namespaces
            for prefix, namespace in self.namespaces.items():
                graph.bind(prefix, namespace)
            
            # Create base URI
            if not base_uri:
                base_uri = f"http://example.org/data/{uuid.uuid4()}"
            
            subject_uri = URIRef(base_uri)
            
            # Convert JSON to RDF
            self._json_to_rdf(graph, subject_uri, json_data)
            
            # Serialize to TTL
            ttl_content = graph.serialize(format="turtle")
            triples_count = len(graph)
            
            logger.info(f"Converted JSON data to TTL: {triples_count} triples")
            
            return TTLConversionResult(
                success=True,
                ttl_content=ttl_content,
                triples_count=triples_count,
                namespaces={prefix: str(ns) for prefix, ns in self.namespaces.items()}
            )
            
        except Exception as e:
            error_msg = f"Error converting JSON to TTL: {str(e)}"
            logger.error(error_msg)
            return TTLConversionResult(
                success=False,
                error_message=error_msg
            )

    def _add_coordinates(self, graph: Graph, subject: URIRef, coordinates: Dict[str, float]):
        """Add geographic coordinates to the graph."""
        if "lat" in coordinates and "lon" in coordinates:
            graph.add((subject, self.GEO.lat, Literal(coordinates["lat"], datatype=XSD.decimal)))
            graph.add((subject, self.GEO.long, Literal(coordinates["lon"], datatype=XSD.decimal)))
        
        if "latitude" in coordinates and "longitude" in coordinates:
            graph.add((subject, self.GEO.lat, Literal(coordinates["latitude"], datatype=XSD.decimal)))
            graph.add((subject, self.GEO.long, Literal(coordinates["longitude"], datatype=XSD.decimal)))

    def _add_metadata(self, graph: Graph, subject: URIRef, metadata: Dict[str, Any]):
        """Add metadata to the graph."""
        for key, value in metadata.items():
            property_uri = self.SCHEMA[f"metadata_{key}"]
            graph.add((subject, property_uri, self._convert_value(value)))

    def _convert_value(self, value: Any) -> Literal:
        """Convert a Python value to an RDF Literal with appropriate datatype."""
        if isinstance(value, bool):
            return Literal(value, datatype=XSD.boolean)
        elif isinstance(value, int):
            return Literal(value, datatype=XSD.integer)
        elif isinstance(value, float):
            return Literal(value, datatype=XSD.decimal)
        elif isinstance(value, datetime):
            return Literal(value.isoformat(), datatype=XSD.dateTime)
        else:
            return Literal(str(value))

    def _json_to_rdf(self, graph: Graph, subject: URIRef, data: Dict[str, Any]):
        """Recursively convert JSON data to RDF triples."""
        for key, value in data.items():
            predicate = self.SCHEMA[key]
            
            if isinstance(value, dict):
                # Create blank node for nested objects
                blank_node = BNode()
                graph.add((subject, predicate, blank_node))
                self._json_to_rdf(graph, blank_node, value)
            elif isinstance(value, list):
                # Handle arrays
                for item in value:
                    if isinstance(item, dict):
                        blank_node = BNode()
                        graph.add((subject, predicate, blank_node))
                        self._json_to_rdf(graph, blank_node, item)
                    else:
                        graph.add((subject, predicate, self._convert_value(item)))
            else:
                graph.add((subject, predicate, self._convert_value(value)))
